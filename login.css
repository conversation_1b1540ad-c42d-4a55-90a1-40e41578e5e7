/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #41535D 0%, #D9DDDF 100%);
    min-height: 100vh;
    color: #1e293b;
    line-height: 1.6;
    overflow-x: hidden;
}

/* Login Container */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    position: relative;
}

/* Background Pattern */
.login-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: linear-gradient(135deg, #41535D 0%, #D9DDDF 100%);
}

.pattern-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    background-size: 100px 100px;
}

/* Login Card */
.login-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    padding: 40px;
    width: 100%;
    max-width: 420px;
    position: relative;
    backdrop-filter: blur(10px);
}

/* Login Header */
.login-header {
    text-align: center;
    margin-bottom: 32px;
}

.logo {
    margin-bottom: 24px;
}

.logo-image {
    height: 50px;
    width: auto;
    max-width: 200px;
    object-fit: contain;
    filter: brightness(0) saturate(100%) invert(27%) sepia(15%) saturate(1084%) hue-rotate(169deg) brightness(95%) contrast(89%);
}

.logo-fallback {
    font-size: 24px;
    font-weight: 700;
    letter-spacing: -0.025em;
    color: #41535D;
}

.login-header h1 {
    font-size: 24px;
    font-weight: 600;
    color: #41535D;
    margin-bottom: 8px;
}

.login-header p {
    color: #64748b;
    font-size: 16px;
}

/* Form Styles */
.login-form {
    margin-bottom: 24px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #374151;
    font-size: 14px;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-wrapper i {
    position: absolute;
    left: 16px;
    color: #64748b;
    font-size: 16px;
    z-index: 2;
}

.input-wrapper input {
    width: 100%;
    padding: 16px 16px 16px 48px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #f8fafc;
}

.input-wrapper input:focus {
    outline: none;
    border-color: #41535D;
    background: white;
    box-shadow: 0 0 0 3px rgba(65, 83, 93, 0.1);
}

.password-toggle {
    position: absolute;
    right: 16px;
    background: none;
    border: none;
    color: #64748b;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: color 0.2s;
    z-index: 2;
}

.password-toggle:hover {
    color: #41535D;
}

/* Login Button */
.login-btn {
    width: 100%;
    padding: 16px;
    background: linear-gradient(135deg, #41535D 0%, #2d3a42 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    position: relative;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(65, 83, 93, 0.4);
}

.login-btn:active {
    transform: translateY(0);
}

.login-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.btn-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Error Message */
.error-message {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
    padding: 12px 16px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    margin-top: 16px;
}

/* Demo Info */
.demo-info {
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 12px;
    padding: 16px;
    margin-top: 24px;
}

.demo-header {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #0369a1;
    font-weight: 600;
    margin-bottom: 12px;
    font-size: 14px;
}

.demo-credentials {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.credential-item {
    font-size: 14px;
    color: #0c4a6e;
    font-family: 'Courier New', monospace;
    background: rgba(14, 165, 233, 0.1);
    padding: 8px 12px;
    border-radius: 6px;
}

/* Footer */
.login-footer {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
}

/* Notifications */
.auth-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    max-width: 400px;
    animation: slideIn 0.3s ease-out;
}

.auth-notification-content {
    background: white;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 12px;
    border-left: 4px solid #10b981;
}

.auth-notification-error .auth-notification-content {
    border-left-color: #ef4444;
}

.auth-notification-close {
    background: none;
    border: none;
    color: #64748b;
    cursor: pointer;
    padding: 4px;
    margin-left: auto;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 480px) {
    .login-card {
        padding: 24px;
        margin: 10px;
    }
    
    .login-header h1 {
        font-size: 20px;
    }
    
    .logo-image {
        height: 40px;
    }
}
