// Login Page JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Check if user is already authenticated
    if (window.authManager.redirectIfAuthenticated()) {
        return; // User is already logged in, redirected to dashboard
    }

    // Initialize login form
    initializeLoginForm();
});

function initializeLoginForm() {
    const loginForm = document.getElementById('loginForm');
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    const passwordToggle = document.getElementById('passwordToggle');
    const loginBtn = document.getElementById('loginBtn');
    const errorMessage = document.getElementById('errorMessage');
    const errorText = document.getElementById('errorText');

    // Password toggle functionality
    passwordToggle.addEventListener('click', function() {
        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordInput.setAttribute('type', type);
        
        const icon = passwordToggle.querySelector('i');
        icon.classList.toggle('fa-eye');
        icon.classList.toggle('fa-eye-slash');
    });

    // Form submission
    loginForm.addEventListener('submit', function(e) {
        e.preventDefault();
        handleLogin();
    });

    // Enter key handling
    [usernameInput, passwordInput].forEach(input => {
        input.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                handleLogin();
            }
        });
    });

    // Clear error message when user starts typing
    [usernameInput, passwordInput].forEach(input => {
        input.addEventListener('input', function() {
            hideErrorMessage();
        });
    });

    function handleLogin() {
        const username = usernameInput.value.trim();
        const password = passwordInput.value;

        // Basic validation
        if (!username || !password) {
            showErrorMessage('Please enter both username and password');
            return;
        }

        // Show loading state
        setLoadingState(true);
        hideErrorMessage();

        // Simulate network delay for better UX
        setTimeout(() => {
            // Attempt authentication
            const result = window.authManager.authenticate(username, password);

            if (result.success) {
                // Success - show notification and redirect
                showNotification('Login successful! Redirecting...', 'success');
                
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 1000);
            } else {
                // Failed - show error
                setLoadingState(false);
                showErrorMessage(result.error);
                
                // Clear password field for security
                passwordInput.value = '';
                passwordInput.focus();
            }
        }, 800); // Simulate network delay
    }

    function setLoadingState(loading) {
        const btnText = loginBtn.querySelector('.btn-text');
        const btnSpinner = loginBtn.querySelector('.btn-spinner');

        if (loading) {
            loginBtn.disabled = true;
            btnText.style.display = 'none';
            btnSpinner.style.display = 'inline-block';
        } else {
            loginBtn.disabled = false;
            btnText.style.display = 'inline-block';
            btnSpinner.style.display = 'none';
        }
    }

    function showErrorMessage(message) {
        errorText.textContent = message;
        errorMessage.style.display = 'flex';
        
        // Add shake animation
        errorMessage.style.animation = 'shake 0.5s ease-in-out';
        setTimeout(() => {
            errorMessage.style.animation = '';
        }, 500);
    }

    function hideErrorMessage() {
        errorMessage.style.display = 'none';
    }

    // Auto-focus username field
    usernameInput.focus();
}

// Add shake animation CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
        20%, 40%, 60%, 80% { transform: translateX(5px); }
    }
`;
document.head.appendChild(style);

// Handle browser back button
window.addEventListener('popstate', function() {
    // Prevent going back to login if already authenticated
    if (window.authManager.isAuthenticated()) {
        window.location.href = 'dashboard.html';
    }
});

// Prevent form resubmission on page refresh
if (window.history.replaceState) {
    window.history.replaceState(null, null, window.location.href);
}
