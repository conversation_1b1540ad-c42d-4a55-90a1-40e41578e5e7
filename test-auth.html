<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Authentication System Test</h1>
        
        <div id="testResults"></div>
        
        <button onclick="runTests()">Run Tests</button>
        <button onclick="clearSession()">Clear Session</button>
        <button onclick="testLogin()">Test Login</button>
        <button onclick="checkAuth()">Check Auth Status</button>
    </div>

    <script src="auth.js"></script>
    <script>
        function addResult(message, isSuccess = true) {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
        }

        function runTests() {
            clearResults();
            
            // Test 1: Check if AuthManager exists
            if (typeof window.authManager !== 'undefined') {
                addResult('✓ AuthManager loaded successfully');
            } else {
                addResult('✗ AuthManager not found', false);
                return;
            }

            // Test 2: Test invalid login
            const invalidResult = window.authManager.authenticate('wrong', 'wrong');
            if (!invalidResult.success) {
                addResult('✓ Invalid credentials rejected correctly');
            } else {
                addResult('✗ Invalid credentials accepted (should fail)', false);
            }

            // Test 3: Test valid login
            const validResult = window.authManager.authenticate('admin', '12345');
            if (validResult.success) {
                addResult('✓ Valid credentials accepted');
            } else {
                addResult('✗ Valid credentials rejected', false);
            }

            // Test 4: Check authentication status
            if (window.authManager.isAuthenticated()) {
                addResult('✓ Authentication status check works');
            } else {
                addResult('✗ Authentication status check failed', false);
            }

            // Test 5: Get current user
            const currentUser = window.authManager.getCurrentUser();
            if (currentUser && currentUser.username === 'admin') {
                addResult('✓ Current user retrieval works');
            } else {
                addResult('✗ Current user retrieval failed', false);
            }

            // Test 6: Test logout
            window.authManager.logout();
            if (!window.authManager.isAuthenticated()) {
                addResult('✓ Logout works correctly');
            } else {
                addResult('✗ Logout failed', false);
            }
        }

        function clearSession() {
            window.authManager.logout();
            addResult('Session cleared');
        }

        function testLogin() {
            clearResults();
            const result = window.authManager.authenticate('admin', '12345');
            if (result.success) {
                addResult('✓ Login successful');
            } else {
                addResult('✗ Login failed: ' + result.error, false);
            }
        }

        function checkAuth() {
            clearResults();
            const isAuth = window.authManager.isAuthenticated();
            const user = window.authManager.getCurrentUser();
            
            addResult(`Authentication Status: ${isAuth ? 'Authenticated' : 'Not Authenticated'}`);
            if (user) {
                addResult(`Current User: ${user.username}`);
                addResult(`Login Time: ${user.loginTime}`);
            }
        }
    </script>
</body>
</html>
