<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WIRTGEN - CDP System</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #41535D 0%, #D9DDDF 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            color: white;
        }
        
        .loading-container {
            text-align: center;
        }
        
        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        
        p {
            margin: 10px 0 0;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="loading-container">
        <div class="spinner"></div>
        <h1>WIRTGEN CDP System</h1>
        <p>Loading...</p>
    </div>

    <script src="auth.js"></script>
    <script>
        // Entry point - redirect based on authentication status
        document.addEventListener('DOMContentLoaded', function() {
            // Small delay to show loading screen
            setTimeout(() => {
                if (window.authManager.isAuthenticated()) {
                    // User is authenticated, redirect to dashboard
                    window.location.href = 'dashboard.html';
                } else {
                    // User is not authenticated, redirect to login
                    window.location.href = 'login.html';
                }
            }, 500);
        });
    </script>
</body>
</html>
