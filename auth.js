// Authentication and Session Management
class AuthManager {
    constructor() {
        this.sessionKey = 'cdp_user_session';
        this.hardcodedCredentials = {
            username: 'admin',
            password: '12345'
        };
    }

    // Authenticate user with hardcoded credentials
    authenticate(username, password) {
        if (username === this.hardcodedCredentials.username && 
            password === this.hardcodedCredentials.password) {
            
            const sessionData = {
                username: username,
                loginTime: new Date().toISOString(),
                isAuthenticated: true
            };
            
            // Store session in localStorage
            localStorage.setItem(this.sessionKey, JSON.stringify(sessionData));
            return { success: true, user: sessionData };
        }
        
        return { success: false, error: 'Invalid username or password' };
    }

    // Check if user is authenticated
    isAuthenticated() {
        try {
            const sessionData = localStorage.getItem(this.sessionKey);
            if (!sessionData) return false;
            
            const session = JSON.parse(sessionData);
            return session && session.isAuthenticated === true;
        } catch (error) {
            console.error('Error checking authentication:', error);
            return false;
        }
    }

    // Get current user session
    getCurrentUser() {
        try {
            const sessionData = localStorage.getItem(this.sessionKey);
            if (!sessionData) return null;
            
            return JSON.parse(sessionData);
        } catch (error) {
            console.error('Error getting current user:', error);
            return null;
        }
    }

    // Logout user
    logout() {
        localStorage.removeItem(this.sessionKey);
        return true;
    }

    // Redirect to login if not authenticated
    requireAuth() {
        if (!this.isAuthenticated()) {
            window.location.href = 'login.html';
            return false;
        }
        return true;
    }

    // Redirect to dashboard if already authenticated
    redirectIfAuthenticated() {
        if (this.isAuthenticated()) {
            window.location.href = 'dashboard.html';
            return true;
        }
        return false;
    }
}

// Create global auth manager instance
window.authManager = new AuthManager();

// Utility function to show notifications
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.auth-notification');
    existingNotifications.forEach(notification => notification.remove());

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `auth-notification auth-notification-${type}`;
    notification.innerHTML = `
        <div class="auth-notification-content">
            <i class="fas ${type === 'error' ? 'fa-exclamation-circle' : 
                           type === 'success' ? 'fa-check-circle' : 'fa-info-circle'}"></i>
            <span>${message}</span>
            <button class="auth-notification-close" onclick="this.parentElement.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}
